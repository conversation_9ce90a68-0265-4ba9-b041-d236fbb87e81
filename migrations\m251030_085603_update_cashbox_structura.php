<?php

use yii\db\Migration;

class m251030_085603_update_cashbox_structura extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        // Удаляем все существующие записи из связанных таблиц
        $this->delete('cashbox_payment_type');
        $this->delete('cashbox_detail');
        $this->delete('cashbox');
        $this->delete('payment_type');

        // Пересоздаем таблицу cashbox с новой структурой
        $this->dropTable('cashbox');
        $this->createTable('cashbox', [
            'id' => $this->bigPrimaryKey(),
            'title' => $this->string()->null(),
            'balance' => $this->decimal(10, 2)->defaultValue(0.00),
            'is_main' => $this->integer()->null(),
            'created_at' => $this->timestamp()->null(),
            'deleted_at' => $this->timestamp()->null(),
        ]);

        // Пересоздаем таблицу cashbox_detail с новой структурой
        $this->dropTable('cashbox_detail');
        $this->createTable('cashbox_detail', [
            'id' => $this->bigPrimaryKey(),
            'cashbox_id' => $this->bigInteger()->notNull(),
            'add_user_id' => $this->bigInteger()->null(),
            'type' => $this->integer()->null(),
            'worker_finance_id' => $this->bigInteger()->null(),
            'amount' => $this->decimal(10, 2),
            'created_at' => $this->timestamp()->null(),
            'deleted_at' => $this->timestamp()->null(),
        ]);

        // Пересоздаем таблицу payment_type
        $this->dropTable('payment_type');
        $this->createTable('payment_type', [
            'id' => $this->bigPrimaryKey(),
            'type' => $this->integer()->null(),
            'created_at' => $this->timestamp()->null(),
            'deleted_at' => $this->timestamp()->null(),
        ]);

        // Пересоздаем таблицу cashbox_payment_type
        $this->dropTable('cashbox_payment_type');
        $this->createTable('cashbox_payment_type', [
            'cashbox_id' => $this->bigInteger()->notNull(),
            'payment_type_id' => $this->bigInteger()->notNull(),
            'created_at' => $this->timestamp()->null(),
            'deleted_at' => $this->timestamp()->null(),
        ]);

        // Добавляем внешние ключи для cashbox_payment_type
        $this->addForeignKey(
            'fk_cashbox_payment_type_cashbox',
            'cashbox_payment_type',
            'cashbox_id',
            'cashbox',
            'id',
            'CASCADE',
            'CASCADE'
        );

        $this->addForeignKey(
            'fk_cashbox_payment_type_payment',
            'cashbox_payment_type',
            'payment_type_id',
            'payment_type',
            'id',
            'CASCADE',
            'CASCADE'
        );

        // Создаем 4 типа платежей
        $this->insert('payment_type', ['id' => 1, 'type' => 1, 'created_at' => date('Y-m-d H:i:s')]); // Наличные
        $this->insert('payment_type', ['id' => 2, 'type' => 2, 'created_at' => date('Y-m-d H:i:s')]); // Перевод
        $this->insert('payment_type', ['id' => 3, 'type' => 3, 'created_at' => date('Y-m-d H:i:s')]); // Терминал
        $this->insert('payment_type', ['id' => 4, 'type' => 4, 'created_at' => date('Y-m-d H:i:s')]); // Платежная карта

        // Создаем 3 кассы
        $this->insert('cashbox', [
            'id' => 1,
            'title' => 'Основной касса',
            'balance' => 0.00,
            'is_main' => 1,
            'created_at' => date('Y-m-d H:i:s')
        ]);

        $this->insert('cashbox', [
            'id' => 2,
            'title' => 'Электронная касса',
            'balance' => 0.00,
            'is_main' => 2,
            'created_at' => date('Y-m-d H:i:s')
        ]);

        $this->insert('cashbox', [
            'id' => 3,
            'title' => 'Оля касса',
            'balance' => 0.00,
            'is_main' => 2,
            'created_at' => date('Y-m-d H:i:s')
        ]);

        // Привязываем типы платежей к кассам
        // Основной касса - только наличные (type = 1)
        $this->insert('cashbox_payment_type', [
            'cashbox_id' => 1,
            'payment_type_id' => 1,
            'created_at' => date('Y-m-d H:i:s')
        ]);

        // Оля касса - только наличные (type = 1)
        $this->insert('cashbox_payment_type', [
            'cashbox_id' => 3,
            'payment_type_id' => 1,
            'created_at' => date('Y-m-d H:i:s')
        ]);

        // Электронная касса - перевод, терминал, платежная карта (type = 2, 3, 4)
        $this->insert('cashbox_payment_type', [
            'cashbox_id' => 2,
            'payment_type_id' => 2,
            'created_at' => date('Y-m-d H:i:s')
        ]);

        $this->insert('cashbox_payment_type', [
            'cashbox_id' => 2,
            'payment_type_id' => 3,
            'created_at' => date('Y-m-d H:i:s')
        ]);

        $this->insert('cashbox_payment_type', [
            'cashbox_id' => 2,
            'payment_type_id' => 4,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        echo "m250710_085603_update_cashbox_structura cannot be reverted.\n";

        return false;
    }


}
